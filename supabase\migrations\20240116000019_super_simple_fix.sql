-- Super Simple Fix - Just create the missing user_integrations table
-- This is the absolute minimum needed to fix the 404 error

-- Create user_integrations table (this is the main one causing 404 errors)
CREATE TABLE IF NOT EXISTS user_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    integration_type TEXT NOT NULL,
    integration_name TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    integration_data JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'pending',
    sync_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, integration_type)
);

-- Create user_activity_logs table if it doesn't exist (for recent activity)
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL,
    event_action TEXT NOT NULL,
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);

-- Enable RLS (required for Supabase)
ALTER TABLE user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

-- Only create policies if they don't exist
DO $$
BEGIN
    -- Policy for user_integrations
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_integrations' AND policyname = 'Users can manage their own integrations') THEN
        CREATE POLICY "Users can manage their own integrations" ON user_integrations
            FOR ALL USING (auth.uid() = user_id);
    END IF;

    -- Policy for user_activity_logs
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_activity_logs' AND policyname = 'Users can view their own activity logs') THEN
        CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_activity_logs' AND policyname = 'Users can insert their own activity logs') THEN
        CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;
END
$$;

-- Simple function to add basic activity data
CREATE OR REPLACE FUNCTION add_sample_activity()
RETURNS TEXT AS $$
DECLARE
    v_user_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN 'Error: User must be authenticated';
    END IF;

    -- Insert sample user activity logs
    INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, timestamp)
    VALUES 
        (v_user_id, 'sample_session', 'navigation', 'page_view', 'visited_dashboard', NOW() - INTERVAL '2 hours'),
        (v_user_id, 'sample_session', 'navigation', 'page_view', 'visited_projects', NOW() - INTERVAL '1 hour'),
        (v_user_id, 'sample_session', 'interaction', 'click', 'created_project', NOW() - INTERVAL '30 minutes')
    ON CONFLICT DO NOTHING;

    RETURN 'Sample activity data added successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION add_sample_activity() TO authenticated;

-- Comments
COMMENT ON TABLE user_integrations IS 'User integrations with external services - fixes 404 error';
COMMENT ON TABLE user_activity_logs IS 'User navigation and interaction logs for recent activity';
COMMENT ON FUNCTION add_sample_activity IS 'Adds basic sample activity data for testing';
