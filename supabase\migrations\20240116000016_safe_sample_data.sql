-- Safe Sample Data Population
-- This migration safely populates sample data with proper column checking

-- Function to safely get project name column
CREATE OR REPLACE FUNCTION get_project_name_column()
RETURNS TEXT AS $$
DECLARE
    col_name TEXT;
BEGIN
    -- Check if 'name' column exists
    SELECT column_name INTO col_name
    FROM information_schema.columns 
    WHERE table_name = 'projects' 
    AND column_name = 'name'
    LIMIT 1;
    
    IF col_name IS NOT NULL THEN
        RETURN 'name';
    END IF;
    
    -- Check if 'title' column exists
    SELECT column_name INTO col_name
    FROM information_schema.columns 
    WHERE table_name = 'projects' 
    AND column_name = 'title'
    LIMIT 1;
    
    IF col_name IS NOT NULL THEN
        RETURN 'title';
    END IF;
    
    -- Check if 'project_name' column exists
    SELECT column_name INTO col_name
    FROM information_schema.columns 
    WHERE table_name = 'projects' 
    AND column_name = 'project_name'
    LIMIT 1;
    
    IF col_name IS NOT NULL THEN
        RETURN 'project_name';
    END IF;
    
    -- Default fallback
    RETURN 'name';
END;
$$ LANGUAGE plpgsql;

-- Safe function to populate sample data for current user
CREATE OR REPLACE FUNCTION populate_my_sample_data_safe()
RETURNS TEXT AS $$
DECLARE
    v_user_id UUID;
    v_project_id UUID;
    v_queue_id UUID;
    v_result TEXT := '';
    v_name_column TEXT;
    v_sql TEXT;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN 'Error: User must be authenticated to populate sample data';
    END IF;
    
    -- Get the correct project name column
    v_name_column := get_project_name_column();
    
    -- Check if user has any projects
    EXECUTE format('SELECT id FROM projects WHERE created_by = $1 LIMIT 1') 
    USING v_user_id INTO v_project_id;
    
    -- Create a sample project if none exists
    IF v_project_id IS NULL THEN
        -- Use dynamic SQL to handle different column names
        v_sql := format('
            INSERT INTO projects (id, %I, description, status, created_by, created_at)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id', v_name_column);
            
        EXECUTE v_sql 
        USING 
            uuid_generate_v4(),
            'Sample Project',
            'A demonstration project to showcase Royaltea features',
            'active',
            v_user_id,
            NOW()
        INTO v_project_id;
        
        v_result := v_result || 'Created sample project. ';
    END IF;

    -- Insert sample user activity logs (only if table exists)
    BEGIN
        INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, from_page, to_page, navigation_method, timestamp)
        VALUES 
            (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_dashboard', null, '/dashboard', 'direct', NOW() - INTERVAL '2 hours'),
            (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_projects', '/dashboard', '/projects', 'click', NOW() - INTERVAL '1 hour'),
            (v_user_id, 'sample_session_1', 'interaction', 'click', 'created_project', '/projects', '/projects', 'click', NOW() - INTERVAL '45 minutes')
        ON CONFLICT DO NOTHING;
        v_result := v_result || 'Added activity logs. ';
    EXCEPTION
        WHEN undefined_table THEN
            v_result := v_result || 'Skipped activity logs (table missing). ';
    END;

    -- Insert sample learning progress (only if table exists)
    BEGIN
        INSERT INTO learning_progress (user_id, course_id, course_provider, course_title, status, completion_percentage, time_spent_minutes, started_at, last_accessed_at)
        VALUES 
            (v_user_id, 'react-fundamentals', 'linkedin_learning', 'React.js Essential Training', 'in_progress', 65.0, 180, NOW() - INTERVAL '7 days', NOW() - INTERVAL '1 day'),
            (v_user_id, 'javascript-advanced', 'youtube', 'Advanced JavaScript Concepts', 'completed', 100.0, 240, NOW() - INTERVAL '14 days', NOW() - INTERVAL '5 days')
        ON CONFLICT DO NOTHING;
        v_result := v_result || 'Added learning progress. ';
    EXCEPTION
        WHEN undefined_table THEN
            v_result := v_result || 'Skipped learning progress (table missing). ';
    END;

    -- Insert sample user skills (only if table exists)
    BEGIN
        INSERT INTO user_skills (user_id, name, level, proficiency_score, verified, verification_date, created_at)
        VALUES 
            (v_user_id, 'JavaScript', 3, 75, true, NOW(), NOW()),
            (v_user_id, 'React', 2, 60, false, null, NOW()),
            (v_user_id, 'CSS', 3, 80, true, NOW(), NOW())
        ON CONFLICT DO NOTHING;
        v_result := v_result || 'Added user skills. ';
    EXCEPTION
        WHEN undefined_table THEN
            v_result := v_result || 'Skipped user skills (table missing). ';
    END;

    -- Insert sample learning content (only if table exists)
    BEGIN
        INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by, created_at)
        VALUES 
            ('tutorial', 'Getting Started with React', 'Learn the basics of React development', 'beginner', 'Development', true, v_user_id, NOW()),
            ('best_practice', 'Clean Code Principles', 'Best practices for writing maintainable code', 'intermediate', 'Development', true, v_user_id, NOW())
        ON CONFLICT DO NOTHING;
        v_result := v_result || 'Added learning content. ';
    EXCEPTION
        WHEN undefined_table THEN
            v_result := v_result || 'Skipped learning content (table missing). ';
    END;

    -- Insert sample learning queue (only if table exists)
    BEGIN
        INSERT INTO learning_queues (user_id, name, description, color, icon, created_at)
        VALUES 
            (v_user_id, 'Frontend Development Path', 'Complete frontend development learning journey', '#3b82f6', 'bi-code-slash', NOW())
        ON CONFLICT DO NOTHING
        RETURNING id INTO v_queue_id;
        
        IF v_queue_id IS NOT NULL THEN
            -- Insert sample queue items
            INSERT INTO learning_queue_items (queue_id, item_type, title, description, url, provider, duration_minutes, difficulty_level, sequence_order, created_at)
            VALUES 
                (v_queue_id, 'video', 'Introduction to Frontend Development', 'Getting started with modern frontend development', 'https://example.com/video/1', 'youtube', 30, 'easy', 1, NOW())
            ON CONFLICT DO NOTHING;
            v_result := v_result || 'Added learning queue and items. ';
        END IF;
    EXCEPTION
        WHEN undefined_table THEN
            v_result := v_result || 'Skipped learning queues (table missing). ';
    END;

    -- Insert sample project activities (only if table exists and project exists)
    IF v_project_id IS NOT NULL THEN
        BEGIN
            INSERT INTO project_activities (project_id, user_id, activity_type, activity_data, created_at)
            VALUES 
                (v_project_id, v_user_id, 'project_created', '{"action": "Created new project", "project_name": "Sample Project"}', NOW() - INTERVAL '2 hours'),
                (v_project_id, v_user_id, 'task_completed', '{"task": "Setup development environment", "hours": 2}', NOW() - INTERVAL '1 hour'),
                (v_project_id, v_user_id, 'milestone_reached', '{"milestone": "Initial setup complete"}', NOW() - INTERVAL '45 minutes')
            ON CONFLICT DO NOTHING;
            v_result := v_result || 'Added project activities. ';
        EXCEPTION
            WHEN undefined_table THEN
                v_result := v_result || 'Skipped project activities (table missing). ';
        END;
    END IF;

    RETURN 'Sample data populated successfully: ' || v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check what tables exist
CREATE OR REPLACE FUNCTION check_table_status()
RETURNS TEXT AS $$
DECLARE
    v_result TEXT := 'Table Status: ';
    v_count INTEGER;
BEGIN
    -- Check each table
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'projects';
    v_result := v_result || 'projects(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'user_activity_logs';
    v_result := v_result || 'user_activity_logs(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'learning_progress';
    v_result := v_result || 'learning_progress(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'user_skills';
    v_result := v_result || 'user_skills(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'learning_content';
    v_result := v_result || 'learning_content(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'learning_queues';
    v_result := v_result || 'learning_queues(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'project_activities';
    v_result := v_result || 'project_activities(' || v_count || ') ';
    
    SELECT COUNT(*) INTO v_count FROM information_schema.tables WHERE table_name = 'user_integrations';
    v_result := v_result || 'user_integrations(' || v_count || ') ';
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION populate_my_sample_data_safe() TO authenticated;
GRANT EXECUTE ON FUNCTION check_table_status() TO authenticated;
GRANT EXECUTE ON FUNCTION get_project_name_column() TO authenticated;

-- Comments
COMMENT ON FUNCTION populate_my_sample_data_safe IS 'Safely populates sample data for the currently authenticated user (handles missing tables and columns)';
COMMENT ON FUNCTION check_table_status IS 'Returns the status of all required tables';
COMMENT ON FUNCTION get_project_name_column IS 'Returns the correct column name for project names (handles name/title/project_name variations)';
