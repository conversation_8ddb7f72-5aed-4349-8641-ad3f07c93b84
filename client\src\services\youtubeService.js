import { supabase } from '../utils/supabase/supabase.utils';

/**
 * YouTube Learning Service
 * 
 * Provides integration with YouTube for educational content within the learning system.
 * Features:
 * - Video search and recommendations
 * - Playlist management for learning paths
 * - Progress tracking for video completion
 * - Integration with course catalog system
 * - Skill-based video recommendations
 */
class YouTubeService {
  constructor() {
    this.apiKey = import.meta.env.REACT_APP_YOUTUBE_API_KEY;
    this.baseUrl = 'https://www.googleapis.com/youtube/v3';
    this.maxResults = 25;
  }

  /**
   * Search YouTube videos for educational content
   */
  async searchVideos(query, filters = {}) {
    try {
      if (!this.apiKey) {
        console.warn('YouTube API key not configured');
        return this.getMockVideos(query);
      }

      const params = new URLSearchParams({
        part: 'snippet,statistics',
        q: `${query} tutorial programming coding`,
        type: 'video',
        videoDuration: filters.duration || 'medium', // short, medium, long
        videoDefinition: 'hd',
        maxResults: filters.maxResults || this.maxResults,
        order: filters.order || 'relevance', // relevance, date, rating, viewCount
        key: this.apiKey
      });

      // Add category filter for educational content
      if (filters.categoryId) {
        params.append('videoCategoryId', filters.categoryId);
      }

      const response = await fetch(`${this.baseUrl}/search?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      return this.formatVideoResults(data.items);

    } catch (error) {
      console.error('Error searching YouTube videos:', error);
      return this.getMockVideos(query);
    }
  }

  /**
   * Get video details by ID
   */
  async getVideoDetails(videoId) {
    try {
      if (!this.apiKey) {
        return this.getMockVideoDetails(videoId);
      }

      const params = new URLSearchParams({
        part: 'snippet,statistics,contentDetails',
        id: videoId,
        key: this.apiKey
      });

      const response = await fetch(`${this.baseUrl}/videos?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      return data.items[0] ? this.formatVideoDetails(data.items[0]) : null;

    } catch (error) {
      console.error('Error getting video details:', error);
      return this.getMockVideoDetails(videoId);
    }
  }

  /**
   * Get playlist videos for learning paths
   */
  async getPlaylistVideos(playlistId) {
    try {
      if (!this.apiKey) {
        return this.getMockPlaylistVideos(playlistId);
      }

      const params = new URLSearchParams({
        part: 'snippet',
        playlistId: playlistId,
        maxResults: 50,
        key: this.apiKey
      });

      const response = await fetch(`${this.baseUrl}/playlistItems?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      return this.formatPlaylistResults(data.items);

    } catch (error) {
      console.error('Error getting playlist videos:', error);
      return this.getMockPlaylistVideos(playlistId);
    }
  }

  /**
   * Get recommended videos based on user skills
   */
  async getRecommendedVideos(skills = []) {
    try {
      const recommendations = [];
      
      for (const skill of skills.slice(0, 3)) { // Limit to top 3 skills
        const videos = await this.searchVideos(skill, { maxResults: 8 });
        recommendations.push(...videos);
      }

      // Remove duplicates and limit results
      const uniqueVideos = recommendations.filter((video, index, self) => 
        index === self.findIndex(v => v.id === video.id)
      );

      return uniqueVideos.slice(0, 20);

    } catch (error) {
      console.error('Error getting recommended videos:', error);
      return this.getMockRecommendedVideos();
    }
  }

  /**
   * Track video progress
   */
  async trackProgress(userId, videoId, progressData) {
    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .upsert({
          user_id: userId,
          course_id: videoId,
          course_provider: 'youtube',
          course_title: progressData.title,
          course_url: `https://www.youtube.com/watch?v=${videoId}`,
          completion_percentage: progressData.percentage || 0,
          time_spent_minutes: progressData.timeSpent || 0,
          status: progressData.percentage >= 90 ? 'completed' : 'in_progress',
          last_accessed_at: new Date().toISOString(),
          progress_data: {
            currentTime: progressData.currentTime,
            duration: progressData.duration,
            watchedSegments: progressData.watchedSegments || []
          }
        }, { onConflict: 'user_id,course_id,course_provider' });

      if (error) throw error;
      return data;

    } catch (error) {
      console.error('Error tracking video progress:', error);
      throw error;
    }
  }

  /**
   * Add video to course catalog
   */
  async addToCatalog(videoData) {
    try {
      const { data, error } = await supabase
        .from('course_catalog')
        .upsert({
          external_id: videoData.id,
          provider: 'youtube',
          title: videoData.title,
          description: videoData.description,
          duration_minutes: videoData.duration,
          instructor_name: videoData.channelTitle,
          thumbnail_url: videoData.thumbnail,
          course_url: `https://www.youtube.com/watch?v=${videoData.id}`,
          skills: videoData.skills || [],
          categories: videoData.categories || [],
          tags: videoData.tags || [],
          is_active: true,
          published_at: videoData.publishedAt
        }, { onConflict: 'external_id,provider' });

      if (error) throw error;
      return data;

    } catch (error) {
      console.error('Error adding video to catalog:', error);
      throw error;
    }
  }

  /**
   * Format video search results
   */
  formatVideoResults(items) {
    return items.map(item => ({
      id: item.id.videoId,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      url: `https://www.youtube.com/watch?v=${item.id.videoId}`,
      embedUrl: `https://www.youtube.com/embed/${item.id.videoId}`,
      provider: 'YouTube'
    }));
  }

  /**
   * Format video details
   */
  formatVideoDetails(item) {
    return {
      id: item.id,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      duration: this.parseDuration(item.contentDetails.duration),
      viewCount: parseInt(item.statistics.viewCount),
      likeCount: parseInt(item.statistics.likeCount),
      url: `https://www.youtube.com/watch?v=${item.id}`,
      embedUrl: `https://www.youtube.com/embed/${item.id}`,
      provider: 'YouTube'
    };
  }

  /**
   * Format playlist results
   */
  formatPlaylistResults(items) {
    return items.map(item => ({
      id: item.snippet.resourceId.videoId,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      url: `https://www.youtube.com/watch?v=${item.snippet.resourceId.videoId}`,
      embedUrl: `https://www.youtube.com/embed/${item.snippet.resourceId.videoId}`,
      provider: 'YouTube'
    }));
  }

  /**
   * Parse YouTube duration format (PT4M13S) to minutes
   */
  parseDuration(duration) {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    const hours = (match[1] || '').replace('H', '') || 0;
    const minutes = (match[2] || '').replace('M', '') || 0;
    const seconds = (match[3] || '').replace('S', '') || 0;
    
    return parseInt(hours) * 60 + parseInt(minutes) + Math.ceil(parseInt(seconds) / 60);
  }

  /**
   * Mock data for development/fallback
   */
  getMockVideos(query) {
    return [
      {
        id: 'dQw4w9WgXcQ',
        title: `${query} - Complete Tutorial`,
        description: `Learn ${query} from scratch with this comprehensive tutorial covering all the fundamentals.`,
        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        channelTitle: 'Tech Education',
        publishedAt: '2024-01-15T10:00:00Z',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        embedUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        provider: 'YouTube'
      }
    ];
  }

  getMockVideoDetails(videoId) {
    return {
      id: videoId,
      title: 'Sample Tutorial Video',
      description: 'This is a sample tutorial video for development purposes.',
      thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
      channelTitle: 'Tech Education',
      publishedAt: '2024-01-15T10:00:00Z',
      duration: 15,
      viewCount: 10000,
      likeCount: 500,
      url: `https://www.youtube.com/watch?v=${videoId}`,
      embedUrl: `https://www.youtube.com/embed/${videoId}`,
      provider: 'YouTube'
    };
  }

  getMockPlaylistVideos(playlistId) {
    return [
      {
        id: 'video1',
        title: 'Introduction to Programming',
        description: 'Learn the basics of programming',
        thumbnail: 'https://img.youtube.com/vi/video1/mqdefault.jpg',
        channelTitle: 'Code Academy',
        publishedAt: '2024-01-10T10:00:00Z',
        url: 'https://www.youtube.com/watch?v=video1',
        embedUrl: 'https://www.youtube.com/embed/video1',
        provider: 'YouTube'
      }
    ];
  }

  getMockRecommendedVideos() {
    return [
      {
        id: 'rec1',
        title: 'Advanced JavaScript Concepts',
        description: 'Master advanced JavaScript concepts',
        thumbnail: 'https://img.youtube.com/vi/rec1/mqdefault.jpg',
        channelTitle: 'JS Mastery',
        publishedAt: '2024-01-12T10:00:00Z',
        url: 'https://www.youtube.com/watch?v=rec1',
        embedUrl: 'https://www.youtube.com/embed/rec1',
        provider: 'YouTube'
      }
    ];
  }
}

export const youtubeService = new YouTubeService();
export default youtubeService;
