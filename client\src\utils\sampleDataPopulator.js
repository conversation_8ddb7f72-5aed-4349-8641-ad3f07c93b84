import { supabase } from './supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Sample Data Populator
 * 
 * Populates the database with sample data for testing and demonstration purposes.
 * This helps ensure the learning system and activity feeds have content to display.
 */
class SampleDataPopulator {
  constructor() {
    this.isPopulating = false;
  }

  /**
   * Populate all sample data for the current user
   */
  async populateAllSampleData(userId) {
    if (this.isPopulating) {
      console.log('Sample data population already in progress');
      return;
    }

    try {
      this.isPopulating = true;
      console.log('🌱 Starting sample data population...');

      // Use the simple database function
      const { error } = await supabase.rpc('populate_my_sample_data');

      if (error) {
        console.warn('Database function failed, using fallback method:', error);
        // Fallback to manual population
        await this.populateLearningProgress(userId);
        await this.populateUserSkills(userId);
        await this.populateRecentActivities(userId);
      } else {
        console.log('✅ Database function completed successfully');
      }

      console.log('✅ Sample data population completed');
      toast.success('Sample data populated successfully!');

    } catch (error) {
      console.error('❌ Error populating sample data:', error);
      toast.error('Failed to populate sample data');
    } finally {
      this.isPopulating = false;
    }
  }

  /**
   * Call a database function safely
   */
  async callDatabaseFunction(functionName, params) {
    try {
      const { error } = await supabase.rpc(functionName, params);
      if (error) {
        console.warn(`Database function ${functionName} failed:`, error);
        // Don't throw - continue with other population methods
      } else {
        console.log(`✅ Database function ${functionName} completed`);
      }
    } catch (error) {
      console.warn(`Database function ${functionName} not available:`, error);
      // Function might not exist yet - that's okay
    }
  }

  /**
   * Populate learning progress data
   */
  async populateLearningProgress(userId) {
    try {
      // Check if learning_progress table exists and has data
      const { data: existingProgress, error: progressError } = await supabase
        .from('learning_progress')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (progressError && !progressError.message.includes('does not exist')) {
        throw progressError;
      }

      // If table exists but no data, populate it
      if (!progressError && (!existingProgress || existingProgress.length === 0)) {
        const sampleProgress = [
          {
            user_id: userId,
            course_id: 'react-fundamentals',
            course_provider: 'linkedin_learning',
            course_title: 'React.js Essential Training',
            status: 'in_progress',
            completion_percentage: 65.0,
            time_spent_minutes: 180,
            started_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
            last_accessed_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
          },
          {
            user_id: userId,
            course_id: 'javascript-advanced',
            course_provider: 'youtube',
            course_title: 'Advanced JavaScript Concepts',
            status: 'completed',
            completion_percentage: 100.0,
            time_spent_minutes: 240,
            started_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
            completed_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
            last_accessed_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            user_id: userId,
            course_id: 'css-grid-mastery',
            course_provider: 'udemy',
            course_title: 'CSS Grid Layout Mastery',
            status: 'not_started',
            completion_percentage: 0.0,
            time_spent_minutes: 0
          }
        ];

        const { error: insertError } = await supabase
          .from('learning_progress')
          .insert(sampleProgress);

        if (insertError) {
          console.warn('Could not insert learning progress:', insertError);
        } else {
          console.log('✅ Learning progress data populated');
        }
      }
    } catch (error) {
      console.warn('Learning progress population skipped:', error.message);
    }
  }

  /**
   * Populate user skills data
   */
  async populateUserSkills(userId) {
    try {
      // Check if user_skills table exists
      const { data: existingSkills, error: skillsError } = await supabase
        .from('user_skills')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (skillsError && !skillsError.message.includes('does not exist')) {
        throw skillsError;
      }

      // If table exists but no data, populate it
      if (!skillsError && (!existingSkills || existingSkills.length === 0)) {
        const sampleSkills = [
          {
            user_id: userId,
            name: 'JavaScript',
            level: 3,
            proficiency_score: 75,
            verified: true,
            verification_date: new Date().toISOString()
          },
          {
            user_id: userId,
            name: 'React',
            level: 2,
            proficiency_score: 60,
            verified: false
          },
          {
            user_id: userId,
            name: 'CSS',
            level: 3,
            proficiency_score: 80,
            verified: true,
            verification_date: new Date().toISOString()
          },
          {
            user_id: userId,
            name: 'Node.js',
            level: 2,
            proficiency_score: 55,
            verified: false
          }
        ];

        const { error: insertError } = await supabase
          .from('user_skills')
          .insert(sampleSkills);

        if (insertError) {
          console.warn('Could not insert user skills:', insertError);
        } else {
          console.log('✅ User skills data populated');
        }
      }
    } catch (error) {
      console.warn('User skills population skipped:', error.message);
    }
  }

  /**
   * Populate recent activities if tables are empty
   */
  async populateRecentActivities(userId) {
    try {
      // Check if we have any recent activities
      const { data: existingActivities, error: activitiesError } = await supabase
        .from('user_activity_logs')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (activitiesError && !activitiesError.message.includes('does not exist')) {
        throw activitiesError;
      }

      // If table exists but no data, populate it
      if (!activitiesError && (!existingActivities || existingActivities.length === 0)) {
        const sessionId = `session_${Date.now()}`;
        const sampleActivities = [
          {
            user_id: userId,
            session_id: sessionId,
            event_type: 'navigation',
            event_category: 'page_view',
            event_action: 'visited_dashboard',
            to_page: '/dashboard',
            navigation_method: 'direct',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
          },
          {
            user_id: userId,
            session_id: sessionId,
            event_type: 'interaction',
            event_category: 'click',
            event_action: 'created_project',
            from_page: '/dashboard',
            to_page: '/projects',
            navigation_method: 'click',
            timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
          },
          {
            user_id: userId,
            session_id: sessionId,
            event_type: 'navigation',
            event_category: 'page_view',
            event_action: 'visited_learning',
            from_page: '/projects',
            to_page: '/learn',
            navigation_method: 'click',
            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
          }
        ];

        const { error: insertError } = await supabase
          .from('user_activity_logs')
          .insert(sampleActivities);

        if (insertError) {
          console.warn('Could not insert activity logs:', insertError);
        } else {
          console.log('✅ Activity logs populated');
        }
      }
    } catch (error) {
      console.warn('Activity logs population skipped:', error.message);
    }
  }

  /**
   * Check if sample data is needed
   */
  async checkIfSampleDataNeeded(userId) {
    try {
      // Use database function if available
      const { data, error } = await supabase.rpc('check_if_sample_data_needed');

      if (!error && data !== null) {
        return data; // Database function returned a boolean
      }

      // Fallback to manual checking
      const checks = await Promise.allSettled([
        supabase.from('learning_progress').select('id', { count: 'exact', head: true }).eq('user_id', userId),
        supabase.from('user_activity_logs').select('id', { count: 'exact', head: true }).eq('user_id', userId),
        supabase.from('user_skills').select('id', { count: 'exact', head: true }).eq('user_id', userId)
      ]);

      // If any table is empty or doesn't exist, we need sample data
      const needsData = checks.some(result => {
        if (result.status === 'rejected') return true; // Table doesn't exist
        return result.value.count === 0; // Table is empty
      });

      return needsData;
    } catch (error) {
      console.warn('Error checking sample data needs:', error);
      return true; // Assume we need data if we can't check
    }
  }

  /**
   * Auto-populate sample data if needed
   */
  async autoPopulateIfNeeded(userId) {
    try {
      const needsData = await this.checkIfSampleDataNeeded(userId);
      
      if (needsData) {
        console.log('🌱 Auto-populating sample data for better user experience...');
        await this.populateAllSampleData(userId);
      } else {
        console.log('✅ User already has sufficient data');
      }
    } catch (error) {
      console.warn('Auto-population check failed:', error);
    }
  }

  /**
   * Clear all sample data for a user (for testing)
   */
  async clearSampleData(userId) {
    try {
      console.log('🧹 Clearing sample data...');

      const tables = [
        'user_activity_logs',
        'learning_progress', 
        'user_skills',
        'project_activities',
        'learning_queues',
        'learning_queue_items'
      ];

      for (const table of tables) {
        try {
          await supabase.from(table).delete().eq('user_id', userId);
        } catch (error) {
          console.warn(`Could not clear ${table}:`, error.message);
        }
      }

      console.log('✅ Sample data cleared');
      toast.success('Sample data cleared successfully!');
    } catch (error) {
      console.error('Error clearing sample data:', error);
      toast.error('Failed to clear sample data');
    }
  }
}

export const sampleDataPopulator = new SampleDataPopulator();
export default sampleDataPopulator;
