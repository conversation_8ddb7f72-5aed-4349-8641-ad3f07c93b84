-- Populate Sample Data for Testing
-- This migration adds sample data to make the system immediately usable

-- Function to populate sample data for a specific user
CREATE OR REPLACE FUNCTION populate_sample_data_for_user(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_project_id UUID;
    v_queue_id UUID;
BEGIN
    -- Create a sample project if none exists
    SELECT id INTO v_project_id
    FROM projects 
    WHERE created_by = p_user_id 
    LIMIT 1;
    
    IF v_project_id IS NULL THEN
        INSERT INTO projects (id, name, description, status, created_by, created_at)
        VALUES (
            uuid_generate_v4(),
            'Sample Project',
            'A demonstration project to showcase Royaltea features',
            'active',
            p_user_id,
            NOW()
        )
        RETURNING id INTO v_project_id;
    END IF;

    -- Insert sample user activity logs
    INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, from_page, to_page, navigation_method, timestamp)
    VALUES 
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_dashboard', null, '/dashboard', 'direct', NOW() - INTERVAL '2 hours'),
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_projects', '/dashboard', '/projects', 'click', NOW() - INTERVAL '1 hour'),
        (p_user_id, 'sample_session_1', 'interaction', 'click', 'created_project', '/projects', '/projects', 'click', NOW() - INTERVAL '45 minutes'),
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_learning', '/projects', '/learn', 'click', NOW() - INTERVAL '30 minutes'),
        (p_user_id, 'sample_session_2', 'navigation', 'page_view', 'visited_track', '/learn', '/track', 'click', NOW() - INTERVAL '15 minutes')
    ON CONFLICT DO NOTHING;

    -- Insert sample project activities
    INSERT INTO project_activities (project_id, user_id, activity_type, activity_data, created_at)
    VALUES 
        (v_project_id, p_user_id, 'project_created', '{"action": "Created new project", "project_name": "Sample Project"}', NOW() - INTERVAL '2 hours'),
        (v_project_id, p_user_id, 'task_completed', '{"task": "Setup development environment", "hours": 2}', NOW() - INTERVAL '1 hour'),
        (v_project_id, p_user_id, 'milestone_reached', '{"milestone": "Initial setup complete"}', NOW() - INTERVAL '45 minutes'),
        (v_project_id, p_user_id, 'collaboration_started', '{"action": "Invited team member"}', NOW() - INTERVAL '30 minutes')
    ON CONFLICT DO NOTHING;

    -- Insert sample learning progress
    INSERT INTO learning_progress (user_id, course_id, course_provider, course_title, status, completion_percentage, time_spent_minutes, started_at, last_accessed_at)
    VALUES 
        (p_user_id, 'react-fundamentals', 'linkedin_learning', 'React.js Essential Training', 'in_progress', 65.0, 180, NOW() - INTERVAL '7 days', NOW() - INTERVAL '1 day'),
        (p_user_id, 'javascript-advanced', 'youtube', 'Advanced JavaScript Concepts', 'completed', 100.0, 240, NOW() - INTERVAL '14 days', NOW() - INTERVAL '5 days'),
        (p_user_id, 'css-grid-mastery', 'udemy', 'CSS Grid Layout Mastery', 'not_started', 0.0, 0, null, null)
    ON CONFLICT DO NOTHING;

    -- Insert sample user skills
    INSERT INTO user_skills (user_id, name, level, proficiency_score, verified, verification_date, created_at)
    VALUES 
        (p_user_id, 'JavaScript', 3, 75, true, NOW(), NOW()),
        (p_user_id, 'React', 2, 60, false, null, NOW()),
        (p_user_id, 'CSS', 3, 80, true, NOW(), NOW()),
        (p_user_id, 'Node.js', 2, 55, false, null, NOW())
    ON CONFLICT DO NOTHING;

    -- Insert sample learning content
    INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by, created_at)
    VALUES 
        ('tutorial', 'Getting Started with React', 'Learn the basics of React development', 'beginner', 'Development', true, p_user_id, NOW()),
        ('best_practice', 'Clean Code Principles', 'Best practices for writing maintainable code', 'intermediate', 'Development', true, p_user_id, NOW()),
        ('documentation', 'API Documentation Guide', 'How to write effective API documentation', 'intermediate', 'Documentation', true, p_user_id, NOW())
    ON CONFLICT DO NOTHING;

    -- Insert sample video recommendations
    INSERT INTO video_recommendations (title, description, video_url, provider, category, recommended_by, recommended_by_name, is_public, created_at)
    VALUES 
        ('React Hooks Explained', 'Comprehensive guide to React Hooks', 'https://youtube.com/watch?v=example1', 'youtube', 'Development', p_user_id, 'Sample User', true, NOW()),
        ('JavaScript ES6 Features', 'Modern JavaScript features you should know', 'https://youtube.com/watch?v=example2', 'youtube', 'Development', p_user_id, 'Sample User', true, NOW()),
        ('CSS Grid Layout', 'Master CSS Grid for modern layouts', 'https://youtube.com/watch?v=example3', 'youtube', 'Design', p_user_id, 'Sample User', true, NOW())
    ON CONFLICT DO NOTHING;

    -- Insert sample learning queue
    INSERT INTO learning_queues (user_id, name, description, color, icon, created_at)
    VALUES 
        (p_user_id, 'Frontend Development Path', 'Complete frontend development learning journey', '#3b82f6', 'bi-code-slash', NOW()),
        (p_user_id, 'Backend Fundamentals', 'Learn backend development basics', '#10b981', 'bi-server', NOW()),
        (p_user_id, 'DevOps Essentials', 'Essential DevOps tools and practices', '#f59e0b', 'bi-gear', NOW())
    ON CONFLICT DO NOTHING;

    -- Get the first learning queue for sample items
    SELECT id INTO v_queue_id
    FROM learning_queues 
    WHERE user_id = p_user_id 
    LIMIT 1;

    -- Insert sample queue items if we have a queue
    IF v_queue_id IS NOT NULL THEN
        INSERT INTO learning_queue_items (queue_id, item_type, title, description, url, provider, duration_minutes, difficulty_level, sequence_order, created_at)
        VALUES 
            (v_queue_id, 'video', 'Introduction to Frontend Development', 'Getting started with modern frontend development', 'https://example.com/video/1', 'youtube', 30, 'easy', 1, NOW()),
            (v_queue_id, 'course', 'HTML & CSS Fundamentals', 'Learn the building blocks of web development', 'https://example.com/course/1', 'udemy', 120, 'easy', 2, NOW()),
            (v_queue_id, 'video', 'JavaScript Basics', 'Introduction to JavaScript programming', 'https://example.com/video/2', 'youtube', 45, 'medium', 3, NOW())
        ON CONFLICT DO NOTHING;
    END IF;

    RAISE NOTICE 'Sample data populated successfully for user %', p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to populate sample data for current authenticated user
CREATE OR REPLACE FUNCTION populate_my_sample_data()
RETURNS VOID AS $$
BEGIN
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to populate sample data';
    END IF;
    
    PERFORM populate_sample_data_for_user(auth.uid());
END;
$$ LANGUAGE plpgsql;

-- Function to check if user needs sample data
CREATE OR REPLACE FUNCTION check_if_sample_data_needed()
RETURNS BOOLEAN AS $$
DECLARE
    v_user_id UUID;
    v_activity_count INTEGER;
    v_learning_count INTEGER;
    v_skills_count INTEGER;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check activity logs
    SELECT COUNT(*) INTO v_activity_count
    FROM user_activity_logs
    WHERE user_id = v_user_id;
    
    -- Check learning progress
    SELECT COUNT(*) INTO v_learning_count
    FROM learning_progress
    WHERE user_id = v_user_id;
    
    -- Check user skills
    SELECT COUNT(*) INTO v_skills_count
    FROM user_skills
    WHERE user_id = v_user_id;
    
    -- Return true if any of these are empty
    RETURN (v_activity_count = 0 OR v_learning_count = 0 OR v_skills_count = 0);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION populate_sample_data_for_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION populate_my_sample_data() TO authenticated;
GRANT EXECUTE ON FUNCTION check_if_sample_data_needed() TO authenticated;

-- Comments
COMMENT ON FUNCTION populate_sample_data_for_user IS 'Populates comprehensive sample data for a specific user to demonstrate platform features';
COMMENT ON FUNCTION populate_my_sample_data IS 'Populates sample data for the currently authenticated user';
COMMENT ON FUNCTION check_if_sample_data_needed IS 'Checks if the current user needs sample data population';
