import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input, Select, SelectItem, Textarea, RadioGroup, Radio, Divider } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Building2, Users, Sparkles, ArrowRight, Plus, User, Building, FileText, MapPin, Phone, Mail, PenTool } from 'lucide-react';

/**
 * StudioSelectionStep Component
 * 
 * Cutting-edge UX for studio selection/creation during project creation
 * Seamlessly integrates studio creation into project workflow
 */
const StudioSelectionStep = ({ 
  userStudios, 
  selectedStudioId, 
  onStudioSelect, 
  onStudioCreated,
  onSkip 
}) => {
  const { currentUser } = useContext(UserContext);
  const [showCreation, setShowCreation] = useState(userStudios.length === 0);
  const [isCreating, setIsCreating] = useState(false);
  
  // Studio creation form data (comprehensive for legal compliance)
  const [currentCreationStep, setCurrentCreationStep] = useState(1);
  const [studioData, setStudioData] = useState({
    // Step 1: Business Type Detection
    business_type: '', // 'individual' or 'established_business'

    // Step 2: Basic Information
    name: '',
    industry: '',

    // Step 3: Legal Entity Information (for established businesses)
    legal_name: '',
    company_type: '', // corporation, llc, partnership, sole_proprietorship
    tax_id: '',
    incorporation_state: '',
    incorporation_date: '',

    // Step 4: Address Information
    address: '',
    city: '',
    state: '',
    zip: '',
    county: '',

    // Step 5: Contact & Signatory Information
    primary_email: '',
    primary_phone: '',
    signer_name: '',
    signer_title: ''
  });

  // Validation for current step
  const validateCurrentStep = () => {
    switch (currentCreationStep) {
      case 1: // Business Type
        return studioData.business_type !== '';
      case 2: // Basic Info
        return studioData.name.trim() && studioData.industry;
      case 3: // Legal Entity (only for established businesses)
        if (studioData.business_type === 'individual') return true;
        return studioData.legal_name.trim() && studioData.company_type;
      case 4: // Address
        return studioData.address.trim() && studioData.city.trim() && studioData.state.trim();
      case 5: // Contact & Signatory
        return studioData.primary_email.trim() && studioData.signer_name.trim();
      default:
        return false;
    }
  };

  // Handle next step in creation wizard
  const handleCreationNext = () => {
    if (!validateCurrentStep()) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Skip legal entity step for individuals
    if (currentCreationStep === 2 && studioData.business_type === 'individual') {
      setCurrentCreationStep(4); // Skip to address
    } else {
      setCurrentCreationStep(currentCreationStep + 1);
    }
  };

  // Handle previous step in creation wizard
  const handleCreationPrevious = () => {
    // Skip legal entity step for individuals when going back
    if (currentCreationStep === 4 && studioData.business_type === 'individual') {
      setCurrentCreationStep(2); // Skip back to basic info
    } else {
      setCurrentCreationStep(currentCreationStep - 1);
    }
  };

  // Handle studio creation with comprehensive legal data
  const handleCreateStudio = async () => {
    if (!validateCurrentStep()) {
      toast.error('Please complete all required information');
      return;
    }

    setIsCreating(true);
    const loadingToastId = toast.loading('Creating your studio...');

    try {
      // Prepare studio data based on business type - using only confirmed schema columns
      const studioPayload = {
        name: studioData.name.trim(),
        description: `${studioData.name} - Professional ${studioData.business_type === 'individual' ? 'Individual' : 'Business'} Studio`,
        created_by: currentUser.id
      };

      // Add optional columns only if they exist in the schema
      if (studioData.business_type) {
        studioPayload.studio_type = studioData.business_type === 'individual' ? 'solo' : 'established';
      }

      if (studioData.industry) {
        studioPayload.industry = studioData.industry;
      }

      // Add business model as JSONB
      studioPayload.business_model = { type: 'collaborative' };

      // Add legal entity information for agreement generation
      studioPayload.legal_entity_info = {
        business_type: studioData.business_type,
        legal_name: studioData.business_type === 'established_business' ? studioData.legal_name : studioData.name,
        company_type: studioData.company_type || 'individual',
        tax_id: studioData.tax_id || null,
        incorporation_state: studioData.incorporation_state || null,
        incorporation_date: studioData.incorporation_date || null
      };

      // Add business address for legal agreements
      studioPayload.business_address = {
        address: studioData.address,
        city: studioData.city,
        state: studioData.state,
        zip: studioData.zip,
        county: studioData.county || `${studioData.city} County`,
        country: 'United States'
      };

      // Add contact information
      studioPayload.contact_information = {
        primary_email: studioData.primary_email,
        primary_phone: studioData.primary_phone || null,
        signer_name: studioData.signer_name,
        signer_title: studioData.signer_title || (studioData.business_type === 'individual' ? 'Owner' : 'Authorized Representative')
      };

      console.log('Creating studio with payload:', studioPayload);

      // Create studio
      const { data: studio, error: studioError } = await supabase
        .from('teams')
        .insert([studioPayload])
        .select()
        .single();

      if (studioError) {
        console.error('Studio creation error:', studioError);
        throw studioError;
      }

      // Add creator as founder
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: studio.id,
          user_id: currentUser.id,
          role: studioData.business_type === 'individual' ? 'owner' : 'founder',
          status: 'active',
          collaboration_type: 'studio_member',
          engagement_duration: 'permanent',
          is_admin: true
        }]);

      if (memberError) throw memberError;

      toast.dismiss(loadingToastId);
      toast.success(`${studioData.business_type === 'individual' ? 'Individual studio' : 'Business studio'} created successfully!`);

      // Notify parent component
      onStudioCreated(studio);

    } catch (error) {
      console.error('Error creating studio:', error);
      toast.dismiss(loadingToastId);
      toast.error('Failed to create studio. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  // Render creation step content
  const renderCreationStep = () => {
    switch (currentCreationStep) {
      case 1: // Business Type Detection
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Building2 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">Let's Set Up Your Studio</h3>
              <p className="text-gray-600 max-w-lg mx-auto">
                First, we need to know if you're creating this as an individual or an established business entity.
                This helps us generate the correct legal agreements.
              </p>
            </div>

            <RadioGroup
              value={studioData.business_type}
              onValueChange={(value) => setStudioData({ ...studioData, business_type: value })}
              className="max-w-lg mx-auto space-y-4"
            >
              <div className="p-4 border-2 border-transparent rounded-xl hover:border-blue-200 transition-colors">
                <Radio value="individual" className="w-full">
                  <div className="flex items-start p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <User className="w-6 h-6 mr-4 text-blue-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900 text-lg mb-1">Individual/Freelancer</div>
                      <div className="text-sm text-gray-600 leading-relaxed">
                        Working as yourself, no formal business entity
                      </div>
                    </div>
                  </div>
                </Radio>
              </div>

              <div className="p-4 border-2 border-transparent rounded-xl hover:border-green-200 transition-colors">
                <Radio value="established_business" className="w-full">
                  <div className="flex items-start p-4 bg-green-50 rounded-lg border border-green-200">
                    <Building className="w-6 h-6 mr-4 text-green-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900 text-lg mb-1">Established Business</div>
                      <div className="text-sm text-gray-600 leading-relaxed">
                        LLC, Corporation, Partnership, or other business entity
                      </div>
                    </div>
                  </div>
                </Radio>
              </div>
            </RadioGroup>
          </div>
        );

      case 2: // Basic Information
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">Studio Basics</h3>
              <p className="text-gray-600">
                {studioData.business_type === 'individual'
                  ? "What should we call your individual studio?"
                  : "Tell us about your business studio."
                }
              </p>
            </div>

            <div className="space-y-4 max-w-lg mx-auto">
              <Input
                label={studioData.business_type === 'individual' ? "Studio Name" : "Studio/Business Name"}
                placeholder={studioData.business_type === 'individual'
                  ? "e.g., John's Creative Studio, Sarah Design Works"
                  : "e.g., Awesome Games LLC, Creative Collective Inc."
                }
                value={studioData.name}
                onChange={(e) => setStudioData({ ...studioData, name: e.target.value })}
                size="lg"
                variant="bordered"
                isRequired
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <Select
                label="Industry"
                placeholder="Select your primary industry"
                selectedKeys={studioData.industry ? [studioData.industry] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  setStudioData({ ...studioData, industry: selectedKey });
                }}
                size="lg"
                variant="bordered"
                isRequired
                classNames={{
                  trigger: "h-14"
                }}
              >
                <SelectItem key="technology" value="technology">💻 Technology & Software</SelectItem>
                <SelectItem key="gaming" value="gaming">🎮 Gaming & Interactive</SelectItem>
                <SelectItem key="creative" value="creative">🎨 Creative & Design</SelectItem>
                <SelectItem key="media" value="media">📱 Media & Content</SelectItem>
                <SelectItem key="consulting" value="consulting">💼 Consulting & Services</SelectItem>
                <SelectItem key="marketing" value="marketing">📈 Marketing & Advertising</SelectItem>
                <SelectItem key="education" value="education">📚 Education & Training</SelectItem>
                <SelectItem key="other" value="other">🔧 Other</SelectItem>
              </Select>
            </div>
          </div>
        );

      case 3: // Legal Entity Information (established businesses only)
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">Legal Entity Details</h3>
              <p className="text-gray-600">
                We need your business entity information to generate legally compliant agreements.
              </p>
            </div>

            <div className="space-y-4 max-w-lg mx-auto">
              <Input
                label="Legal Business Name"
                placeholder="e.g., Awesome Games LLC, Creative Collective Inc."
                value={studioData.legal_name}
                onChange={(e) => setStudioData({ ...studioData, legal_name: e.target.value })}
                size="lg"
                variant="bordered"
                isRequired
                description="The official registered name of your business"
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <Select
                label="Business Entity Type"
                placeholder="Select your business structure"
                selectedKeys={studioData.company_type ? [studioData.company_type] : []}
                onSelectionChange={(keys) => {
                  const selectedKey = Array.from(keys)[0];
                  setStudioData({ ...studioData, company_type: selectedKey });
                }}
                size="lg"
                variant="bordered"
                isRequired
                classNames={{
                  trigger: "h-14"
                }}
              >
                <SelectItem key="llc" value="llc">🏢 Limited Liability Company (LLC)</SelectItem>
                <SelectItem key="corporation" value="corporation">🏛️ Corporation</SelectItem>
                <SelectItem key="partnership" value="partnership">🤝 Partnership</SelectItem>
                <SelectItem key="sole_proprietorship" value="sole_proprietorship">👤 Sole Proprietorship</SelectItem>
              </Select>

              <Input
                label="Tax ID / EIN (Optional)"
                placeholder="e.g., 12-3456789"
                value={studioData.tax_id}
                onChange={(e) => setStudioData({ ...studioData, tax_id: e.target.value })}
                size="lg"
                variant="bordered"
                description="Your Employer Identification Number if you have one"
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <Input
                label="State of Incorporation (Optional)"
                placeholder="e.g., Delaware, California, New York"
                value={studioData.incorporation_state}
                onChange={(e) => setStudioData({ ...studioData, incorporation_state: e.target.value })}
                size="lg"
                variant="bordered"
                description="The state where your business is registered"
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />
            </div>
          </div>
        );

      case 4: // Address Information
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
                <MapPin className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">Business Address</h3>
              <p className="text-gray-600">
                We need your business address for legal agreements and compliance.
              </p>
            </div>

            <div className="space-y-4 max-w-lg mx-auto">
              <Input
                label="Street Address"
                placeholder="e.g., 123 Main Street, Suite 100"
                value={studioData.address}
                onChange={(e) => setStudioData({ ...studioData, address: e.target.value })}
                size="lg"
                variant="bordered"
                isRequired
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="City"
                  placeholder="e.g., San Francisco"
                  value={studioData.city}
                  onChange={(e) => setStudioData({ ...studioData, city: e.target.value })}
                  size="lg"
                  variant="bordered"
                  isRequired
                  classNames={{
                    input: "text-lg",
                    inputWrapper: "h-14"
                  }}
                />

                <Input
                  label="State"
                  placeholder="e.g., CA"
                  value={studioData.state}
                  onChange={(e) => setStudioData({ ...studioData, state: e.target.value })}
                  size="lg"
                  variant="bordered"
                  isRequired
                  classNames={{
                    input: "text-lg",
                    inputWrapper: "h-14"
                  }}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="ZIP Code (Optional)"
                  placeholder="e.g., 94105"
                  value={studioData.zip}
                  onChange={(e) => setStudioData({ ...studioData, zip: e.target.value })}
                  size="lg"
                  variant="bordered"
                  classNames={{
                    input: "text-lg",
                    inputWrapper: "h-14"
                  }}
                />

                <Input
                  label="County (Optional)"
                  placeholder="e.g., San Francisco County"
                  value={studioData.county}
                  onChange={(e) => setStudioData({ ...studioData, county: e.target.value })}
                  size="lg"
                  variant="bordered"
                  classNames={{
                    input: "text-lg",
                    inputWrapper: "h-14"
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 5: // Contact & Signatory Information
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-teal-500 to-cyan-600 flex items-center justify-center">
                <PenTool className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">Contact & Signatory</h3>
              <p className="text-gray-600">
                Who will be the authorized person to sign legal agreements?
              </p>
            </div>

            <div className="space-y-4 max-w-lg mx-auto">
              <Input
                label="Primary Email"
                placeholder="e.g., <EMAIL>"
                type="email"
                value={studioData.primary_email}
                onChange={(e) => setStudioData({ ...studioData, primary_email: e.target.value })}
                size="lg"
                variant="bordered"
                isRequired
                description="This will be used for all business communications"
                startContent={<Mail className="w-4 h-4 text-default-400" />}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <Input
                label="Phone Number (Optional)"
                placeholder="e.g., (*************"
                type="tel"
                value={studioData.primary_phone}
                onChange={(e) => setStudioData({ ...studioData, primary_phone: e.target.value })}
                size="lg"
                variant="bordered"
                startContent={<Phone className="w-4 h-4 text-default-400" />}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
              />

              <Divider className="my-6" />

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 text-blue-900">Authorized Signatory</h4>
                <p className="text-sm text-blue-700 mb-4">
                  This person will have the authority to sign legal agreements on behalf of the {studioData.business_type === 'individual' ? 'studio' : 'business'}.
                </p>

                <div className="space-y-3">
                  <Input
                    label="Full Name"
                    placeholder="e.g., John Smith"
                    value={studioData.signer_name}
                    onChange={(e) => setStudioData({ ...studioData, signer_name: e.target.value })}
                    size="lg"
                    variant="bordered"
                    isRequired
                    classNames={{
                      input: "text-lg",
                      inputWrapper: "h-14"
                    }}
                  />

                  <Input
                    label="Title/Position"
                    placeholder={studioData.business_type === 'individual' ? "e.g., Owner, Founder" : "e.g., CEO, President, Managing Member"}
                    value={studioData.signer_title}
                    onChange={(e) => setStudioData({ ...studioData, signer_title: e.target.value })}
                    size="lg"
                    variant="bordered"
                    classNames={{
                      input: "text-lg",
                      inputWrapper: "h-14"
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header with contextual explanation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold mb-3 text-gray-900 dark:text-white">Choose Your Studio</h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Studios help us generate legal agreements and manage your projects.
            {userStudios.length === 0
              ? " Let's create your first studio with just a few questions."
              : " Select an existing studio or create a new one."
            }
          </p>
        </motion.div>

      <AnimatePresence mode="wait">
        {showCreation ? (
          // Comprehensive Studio Creation Wizard
          <motion.div
            key="creation"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-200">
              <CardBody className="p-8">
                {/* Progress Indicator */}
                <div className="flex items-center justify-center mb-8">
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((step) => (
                      <div key={step} className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          step < currentCreationStep ? 'bg-green-500 text-white' :
                          step === currentCreationStep ? 'bg-blue-500 text-white' :
                          'bg-gray-200 text-gray-500'
                        }`}>
                          {step < currentCreationStep ? '✓' : step}
                        </div>
                        {step < 5 && (
                          <div className={`w-8 h-1 mx-1 ${
                            step < currentCreationStep ? 'bg-green-500' : 'bg-gray-200'
                          }`} />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Step Content */}
                {renderCreationStep()}

                {/* Navigation Buttons */}
                <div className="flex justify-between items-center mt-8">
                  <Button
                    variant="bordered"
                    size="lg"
                    onClick={currentCreationStep === 1 ? () => setShowCreation(false) : handleCreationPrevious}
                    disabled={isCreating}
                    className="px-6"
                  >
                    {currentCreationStep === 1 ? 'Cancel' : 'Previous'}
                  </Button>

                  <div className="text-sm text-gray-500">
                    Step {currentCreationStep} of 5
                  </div>

                  {currentCreationStep < 5 ? (
                    <Button
                      color="primary"
                      size="lg"
                      onClick={handleCreationNext}
                      disabled={!validateCurrentStep() || isCreating}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold px-8"
                      endContent={<ArrowRight className="w-4 h-4" />}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      color="success"
                      size="lg"
                      onClick={handleCreateStudio}
                      isLoading={isCreating}
                      disabled={!validateCurrentStep()}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold px-8"
                      endContent={<Sparkles className="w-4 h-4" />}
                    >
                      Create Studio
                    </Button>
                  )}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ) : (
          // Studio Selection
          <motion.div
            key="selection"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid gap-4 mb-6">
              {userStudios.map((studio) => (
                <Card
                  key={studio.id}
                  isPressable
                  className={`transition-all duration-200 ${
                    selectedStudioId === studio.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => onStudioSelect(studio.id)}
                >
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center mr-4">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold">{studio.name}</h3>
                          <p className="text-sm text-gray-500">
                            {studio.studio_type} • {studio.industry}
                          </p>
                        </div>
                      </div>
                      
                      {selectedStudioId === studio.id && (
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>

            {/* Create New Studio Option */}
            <Card
              isPressable
              className="border-2 border-dashed border-default-300 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200"
              onClick={() => setShowCreation(true)}
            >
              <CardBody className="p-6 text-center">
                <div className="flex items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mr-4">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Create New Studio</h3>
                    <p className="text-sm text-gray-500">Start a new studio for this project</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </div>
  );
};

export default StudioSelectionStep;
