import React, { useState, useEffect, useContext } from 'react';
import { 
  Card, CardBody, CardHeader, Button, Progress, Chip, 
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  useDisclosure
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import sampleDataPopulator from '../../utils/sampleDataPopulator';

/**
 * System Data Initializer Component
 * 
 * Detects when the system has empty data and offers to populate
 * sample data for a better user experience. This helps with:
 * - Empty learning pages
 * - Empty recent activity feeds
 * - Missing user skills and progress
 */
const SystemDataInitializer = () => {
  const { currentUser } = useContext(UserContext);
  const [needsData, setNeedsData] = useState(false);
  const [loading, setLoading] = useState(true);
  const [populating, setPopulating] = useState(false);
  const [dataStatus, setDataStatus] = useState({});
  const { isOpen, onOpen, onClose } = useDisclosure();

  useEffect(() => {
    if (currentUser) {
      checkDataStatus();
    }
  }, [currentUser]);

  const checkDataStatus = async () => {
    try {
      setLoading(true);
      
      const needsSampleData = await sampleDataPopulator.checkIfSampleDataNeeded(currentUser.id);
      setNeedsData(needsSampleData);
      
      // Get detailed status for display
      const status = await getDetailedDataStatus();
      setDataStatus(status);
      
      // Auto-show modal if user has very little data
      if (needsSampleData && Object.values(status).filter(s => s.count === 0).length >= 3) {
        onOpen();
      }
      
    } catch (error) {
      console.error('Error checking data status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDetailedDataStatus = async () => {
    const status = {};
    
    const tables = [
      { key: 'learning', table: 'learning_progress', name: 'Learning Progress' },
      { key: 'activities', table: 'user_activity_logs', name: 'Recent Activities' },
      { key: 'skills', table: 'user_skills', name: 'User Skills' },
      { key: 'projects', table: 'projects', name: 'Projects', column: 'created_by' },
      { key: 'queues', table: 'learning_queues', name: 'Learning Queues' }
    ];

    for (const tableInfo of tables) {
      try {
        const { supabase } = await import('../../utils/supabase/supabase.utils');
        const column = tableInfo.column || 'user_id';
        
        const { count, error } = await supabase
          .from(tableInfo.table)
          .select('id', { count: 'exact', head: true })
          .eq(column, currentUser.id);

        status[tableInfo.key] = {
          name: tableInfo.name,
          count: error ? 0 : (count || 0),
          hasData: !error && count > 0,
          error: error?.message
        };
      } catch (error) {
        status[tableInfo.key] = {
          name: tableInfo.name,
          count: 0,
          hasData: false,
          error: error.message
        };
      }
    }

    return status;
  };

  const handlePopulateSampleData = async () => {
    try {
      setPopulating(true);
      await sampleDataPopulator.populateAllSampleData(currentUser.id);
      
      // Refresh data status
      await checkDataStatus();
      
      onClose();
      
      // Suggest page refresh for immediate effect
      toast.success('Sample data populated! Refresh the page to see changes.', {
        duration: 5000,
        action: {
          label: 'Refresh',
          onClick: () => window.location.reload()
        }
      });
      
    } catch (error) {
      console.error('Error populating sample data:', error);
      toast.error('Failed to populate sample data');
    } finally {
      setPopulating(false);
    }
  };

  const handleClearSampleData = async () => {
    try {
      setPopulating(true);
      await sampleDataPopulator.clearSampleData(currentUser.id);
      
      // Refresh data status
      await checkDataStatus();
      
      toast.success('Sample data cleared! Refresh the page to see changes.', {
        duration: 5000,
        action: {
          label: 'Refresh',
          onClick: () => window.location.reload()
        }
      });
      
    } catch (error) {
      console.error('Error clearing sample data:', error);
      toast.error('Failed to clear sample data');
    } finally {
      setPopulating(false);
    }
  };

  if (loading) {
    return (
      <Card className="mb-4">
        <CardBody className="p-4">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm">Checking system data...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!needsData) {
    return null; // Don't show anything if user has sufficient data
  }

  return (
    <>
      {/* Data Status Banner */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <Card className="border-warning bg-warning/5">
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-warning/20 rounded-full">
                  <i className="bi bi-info-circle text-warning text-lg"></i>
                </div>
                <div>
                  <h3 className="font-semibold text-warning">System Data Initialization</h3>
                  <p className="text-sm text-default-600">
                    Some features may appear empty. Would you like to populate sample data for a better experience?
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="flat"
                  color="warning"
                  onPress={onOpen}
                >
                  View Details
                </Button>
                <Button
                  size="sm"
                  color="warning"
                  onPress={handlePopulateSampleData}
                  isLoading={populating}
                >
                  Populate Sample Data
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Detailed Status Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">System Data Status</h3>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-4">
              <p className="text-default-600">
                Here's the current status of your data across different system components:
              </p>

              <div className="space-y-3">
                {Object.entries(dataStatus).map(([key, status]) => (
                  <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        status.hasData ? 'bg-success' : 'bg-warning'
                      }`}></div>
                      <div>
                        <div className="font-medium">{status.name}</div>
                        {status.error && (
                          <div className="text-xs text-danger">{status.error}</div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <Chip
                        size="sm"
                        color={status.hasData ? 'success' : 'warning'}
                        variant="flat"
                      >
                        {status.count} items
                      </Chip>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-4 bg-default-100 rounded-lg">
                <h4 className="font-semibold mb-2">What will sample data include?</h4>
                <ul className="text-sm text-default-600 space-y-1">
                  <li>• Sample learning progress and courses</li>
                  <li>• Recent activity logs for dashboard</li>
                  <li>• User skills and proficiency levels</li>
                  <li>• Learning queues and educational content</li>
                  <li>• Project activities and collaboration data</li>
                </ul>
              </div>

              <div className="p-4 bg-warning/10 border border-warning/20 rounded-lg">
                <div className="flex items-start gap-2">
                  <i className="bi bi-exclamation-triangle text-warning mt-0.5"></i>
                  <div className="text-sm">
                    <strong>Note:</strong> Sample data is for demonstration purposes only. 
                    You can clear it at any time and replace it with your real data as you use the platform.
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>
          
          <ModalFooter>
            <div className="flex gap-2 w-full">
              <Button
                color="danger"
                variant="flat"
                onPress={handleClearSampleData}
                isLoading={populating}
                className="flex-1"
              >
                Clear Sample Data
              </Button>
              <Button
                color="warning"
                onPress={handlePopulateSampleData}
                isLoading={populating}
                className="flex-1"
              >
                Populate Sample Data
              </Button>
              <Button
                variant="flat"
                onPress={onClose}
                className="flex-1"
              >
                Close
              </Button>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default SystemDataInitializer;
