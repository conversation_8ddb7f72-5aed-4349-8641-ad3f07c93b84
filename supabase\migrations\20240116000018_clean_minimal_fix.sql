-- Clean Minimal Fix - Just create the essential missing tables
-- This migration only creates the tables that are causing 404 errors

-- Create user_integrations table (this is the main one causing 404 errors)
CREATE TABLE IF NOT EXISTS user_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    integration_type TEXT NOT NULL,
    integration_name TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP WITH TIME ZONE,
    integration_data JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT DEFAULT 'pending',
    sync_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, integration_type)
);

-- Create user_skills table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    level INTEGER DEFAULT 1,
    proficiency_score INTEGER DEFAULT 0,
    verified BOOLEAN DEFAULT false,
    verification_date TIMESTAMP WITH TIME ZONE,
    verification_method TEXT,
    endorsements INTEGER DEFAULT 0,
    learning_hours INTEGER DEFAULT 0,
    practice_hours INTEGER DEFAULT 0,
    project_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    skill_category TEXT,
    skill_tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Create learning_progress table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    course_id TEXT NOT NULL,
    course_provider TEXT NOT NULL,
    course_title TEXT,
    course_url TEXT,
    status TEXT DEFAULT 'not_started',
    completion_percentage DECIMAL(5,2) DEFAULT 0.0,
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_data JSONB DEFAULT '{}'::jsonb,
    certificate_url TEXT,
    certificate_earned BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, course_id, course_provider)
);

-- Create user_activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL,
    event_category TEXT NOT NULL,
    event_action TEXT NOT NULL,
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    content_body TEXT,
    difficulty_level TEXT DEFAULT 'medium',
    category TEXT,
    tags TEXT[],
    vote_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);

-- Enable RLS on all tables
ALTER TABLE user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;

-- Create simple RLS policies
CREATE POLICY "Users can manage their own integrations" ON user_integrations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own skills" ON user_skills
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own learning progress" ON learning_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can view active learning content" ON learning_content
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can create learning content" ON learning_content
    FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Simple function to populate basic sample data
CREATE OR REPLACE FUNCTION populate_basic_sample_data()
RETURNS TEXT AS $$
DECLARE
    v_user_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN 'Error: User must be authenticated';
    END IF;

    -- Insert sample user activity logs
    INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, timestamp)
    VALUES 
        (v_user_id, 'sample_session', 'navigation', 'page_view', 'visited_dashboard', NOW() - INTERVAL '2 hours'),
        (v_user_id, 'sample_session', 'navigation', 'page_view', 'visited_projects', NOW() - INTERVAL '1 hour'),
        (v_user_id, 'sample_session', 'interaction', 'click', 'created_project', NOW() - INTERVAL '30 minutes')
    ON CONFLICT DO NOTHING;

    -- Insert sample learning progress
    INSERT INTO learning_progress (user_id, course_id, course_provider, course_title, status, completion_percentage, time_spent_minutes)
    VALUES 
        (v_user_id, 'react-basics', 'youtube', 'React Fundamentals', 'in_progress', 65.0, 180),
        (v_user_id, 'js-advanced', 'linkedin_learning', 'Advanced JavaScript', 'completed', 100.0, 240)
    ON CONFLICT DO NOTHING;

    -- Insert sample user skills
    INSERT INTO user_skills (user_id, name, level, proficiency_score, verified)
    VALUES 
        (v_user_id, 'JavaScript', 3, 75, true),
        (v_user_id, 'React', 2, 60, false),
        (v_user_id, 'CSS', 3, 80, true)
    ON CONFLICT DO NOTHING;

    -- Insert sample learning content
    INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by)
    VALUES 
        ('tutorial', 'Getting Started with React', 'Learn React basics', 'beginner', 'Development', true, v_user_id),
        ('best_practice', 'Clean Code Principles', 'Writing maintainable code', 'intermediate', 'Development', true, v_user_id)
    ON CONFLICT DO NOTHING;

    RETURN 'Basic sample data populated successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION populate_basic_sample_data() TO authenticated;

-- Comments
COMMENT ON TABLE user_integrations IS 'User integrations with external services';
COMMENT ON TABLE user_skills IS 'User skills and proficiency levels';
COMMENT ON TABLE learning_progress IS 'User progress through learning courses';
COMMENT ON TABLE user_activity_logs IS 'User navigation and interaction logs';
COMMENT ON TABLE learning_content IS 'Educational content and tutorials';
COMMENT ON FUNCTION populate_basic_sample_data IS 'Populates basic sample data for testing';
