import { supabase } from '../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Task-Gig Integration Service
 * 
 * Handles conversion between tasks and gigs, manages relationships,
 * and provides seamless integration between task management and gigwork systems.
 */
class TaskGigIntegrationService {
  constructor() {
    this.defaultBudgetRanges = {
      'easy': { min: 50, max: 200 },
      'medium': { min: 200, max: 500 },
      'hard': { min: 500, max: 1500 },
      'expert': { min: 1500, max: 5000 }
    };
    
    this.defaultTimelineMapping = {
      'easy': 3,
      'medium': 7,
      'hard': 14,
      'expert': 30
    };
  }

  /**
   * Convert a task to a gig
   */
  async convertTaskToGig(taskId, options = {}) {
    try {
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .select(`
          *,
          projects(id, name, title)
        `)
        .eq('id', taskId)
        .single();

      if (taskError) throw taskError;
      if (!task) throw new Error('Task not found');

      // Determine budget range based on difficulty
      const budgetRange = this.defaultBudgetRanges[task.difficulty_level] || this.defaultBudgetRanges.medium;
      const timeline = this.defaultTimelineMapping[task.difficulty_level] || 7;

      // Prepare gig data
      const gigData = {
        title: options.title || task.title,
        description: options.description || this.generateGigDescription(task),
        project_id: task.project_id,
        request_type: 'task_assistance',
        budget_min: options.budgetMin || budgetRange.min,
        budget_max: options.budgetMax || budgetRange.max,
        timeline_days: options.timelineDays || timeline,
        skill_requirements: options.skillRequirements || this.extractSkillRequirements(task),
        priority_level: this.mapTaskPriorityToGig(task.difficulty_level),
        source_task_id: taskId,
        auto_assign_on_acceptance: options.autoAssign !== false,
        task_integration_settings: {
          original_task_id: taskId,
          auto_sync_enabled: options.autoSync !== false,
          conversion_date: new Date().toISOString(),
          conversion_reason: options.reason || 'Task requires external assistance'
        }
      };

      // Use database function for conversion
      const { data: gigId, error: conversionError } = await supabase
        .rpc('convert_task_to_gig', {
          p_task_id: taskId,
          p_user_id: (await supabase.auth.getUser()).data.user.id,
          p_budget_min: gigData.budget_min,
          p_budget_max: gigData.budget_max,
          p_timeline_days: gigData.timeline_days,
          p_skill_requirements: gigData.skill_requirements,
          p_conversion_reason: options.reason
        });

      if (conversionError) throw conversionError;

      toast.success('Task converted to gig successfully!');
      return { gigId, originalTask: task };

    } catch (error) {
      console.error('Error converting task to gig:', error);
      toast.error('Failed to convert task to gig');
      throw error;
    }
  }

  /**
   * Convert a gig to a task
   */
  async convertGigToTask(gigId, projectId, options = {}) {
    try {
      const { data: gig, error: gigError } = await supabase
        .from('collaboration_requests')
        .select('*')
        .eq('id', gigId)
        .single();

      if (gigError) throw gigError;
      if (!gig) throw new Error('Gig not found');

      // Use database function for conversion
      const { data: taskId, error: conversionError } = await supabase
        .rpc('convert_gig_to_task', {
          p_gig_id: gigId,
          p_user_id: (await supabase.auth.getUser()).data.user.id,
          p_project_id: projectId,
          p_assignee_id: options.assigneeId || null,
          p_conversion_reason: options.reason
        });

      if (conversionError) throw conversionError;

      toast.success('Gig converted to task successfully!');
      return { taskId, originalGig: gig };

    } catch (error) {
      console.error('Error converting gig to task:', error);
      toast.error('Failed to convert gig to task');
      throw error;
    }
  }

  /**
   * Get task-gig relationships for a user
   */
  async getUserTaskGigRelationships(userId) {
    try {
      const { data, error } = await supabase
        .from('task_gig_relationships')
        .select(`
          *,
          task:tasks(*),
          gig:collaboration_requests(*)
        `)
        .eq('converted_by', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error fetching task-gig relationships:', error);
      return [];
    }
  }

  /**
   * Get gig assignments for a user
   */
  async getUserGigAssignments(userId) {
    try {
      const { data, error } = await supabase
        .from('gig_task_assignments')
        .select(`
          *,
          gig:collaboration_requests(*),
          task:tasks(*),
          application:collaboration_applications(*)
        `)
        .eq('assigned_user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error fetching gig assignments:', error);
      return [];
    }
  }

  /**
   * Update task-gig sync settings
   */
  async updateSyncSettings(relationshipId, settings) {
    try {
      const { error } = await supabase
        .from('task_gig_relationships')
        .update({
          auto_sync_status: settings.autoSyncStatus,
          auto_sync_assignee: settings.autoSyncAssignee,
          auto_sync_deadline: settings.autoSyncDeadline,
          updated_at: new Date().toISOString()
        })
        .eq('id', relationshipId);

      if (error) throw error;

      toast.success('Sync settings updated successfully!');
      return true;

    } catch (error) {
      console.error('Error updating sync settings:', error);
      toast.error('Failed to update sync settings');
      return false;
    }
  }

  /**
   * Break task-gig relationship
   */
  async breakRelationship(relationshipId, reason = '') {
    try {
      const { error } = await supabase
        .from('task_gig_relationships')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', relationshipId);

      if (error) throw error;

      toast.success('Task-gig relationship disconnected');
      return true;

    } catch (error) {
      console.error('Error breaking relationship:', error);
      toast.error('Failed to disconnect relationship');
      return false;
    }
  }

  /**
   * Get conversion templates
   */
  async getConversionTemplates(category = null) {
    try {
      let query = supabase
        .from('task_gig_templates')
        .select('*')
        .eq('is_active', true)
        .order('usage_count', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];

    } catch (error) {
      console.error('Error fetching templates:', error);
      return [];
    }
  }

  /**
   * Apply template to task conversion
   */
  async applyTemplate(templateId, taskId) {
    try {
      const { data: template, error: templateError } = await supabase
        .from('task_gig_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError) throw templateError;

      // Increment usage count
      await supabase
        .from('task_gig_templates')
        .update({ usage_count: template.usage_count + 1 })
        .eq('id', templateId);

      // Apply template settings to conversion
      return this.convertTaskToGig(taskId, {
        budgetMin: template.default_budget_min,
        budgetMax: template.default_budget_max,
        timelineDays: template.default_timeline_days,
        skillRequirements: template.default_skill_requirements,
        reason: `Applied template: ${template.name}`
      });

    } catch (error) {
      console.error('Error applying template:', error);
      throw error;
    }
  }

  /**
   * Generate gig description from task
   */
  generateGigDescription(task) {
    const baseDescription = task.description || 'Task requires completion';
    
    const additionalInfo = [
      `**Original Task:** ${task.title}`,
      `**Difficulty Level:** ${task.difficulty_level}`,
      `**Estimated Hours:** ${task.estimated_hours || 'TBD'}`,
      '',
      '**Requirements:**',
      '- Complete the task as specified',
      '- Provide regular progress updates',
      '- Deliver high-quality work',
      '- Communicate effectively with the team',
      '',
      '**Deliverables:**',
      '- Completed task implementation',
      '- Documentation (if applicable)',
      '- Testing and quality assurance'
    ];

    return `${baseDescription}\n\n${additionalInfo.join('\n')}`;
  }

  /**
   * Extract skill requirements from task
   */
  extractSkillRequirements(task) {
    const skillMap = {
      'development': ['JavaScript', 'Programming', 'Problem Solving'],
      'design': ['UI Design', 'UX Design', 'Creative Thinking'],
      'content': ['Writing', 'Content Creation', 'Communication'],
      'marketing': ['Digital Marketing', 'Analytics', 'Strategy'],
      'research': ['Research', 'Analysis', 'Critical Thinking']
    };

    return skillMap[task.task_type] || ['General Skills', 'Problem Solving'];
  }

  /**
   * Map task priority to gig priority
   */
  mapTaskPriorityToGig(difficulty) {
    const priorityMap = {
      'easy': 'low',
      'medium': 'medium',
      'hard': 'high',
      'expert': 'urgent'
    };

    return priorityMap[difficulty] || 'medium';
  }

  /**
   * Get integration statistics
   */
  async getIntegrationStats(userId) {
    try {
      const [relationships, assignments] = await Promise.all([
        this.getUserTaskGigRelationships(userId),
        this.getUserGigAssignments(userId)
      ]);

      const stats = {
        totalConversions: relationships.length,
        tasksToGigs: relationships.filter(r => r.relationship_type === 'task_to_gig').length,
        gigsToTasks: relationships.filter(r => r.relationship_type === 'gig_to_task').length,
        activeAssignments: assignments.filter(a => a.status === 'assigned' || a.status === 'in_progress').length,
        completedAssignments: assignments.filter(a => a.status === 'completed').length,
        totalEarnings: assignments
          .filter(a => a.status === 'completed' && a.agreed_rate)
          .reduce((sum, a) => sum + parseFloat(a.agreed_rate), 0)
      };

      return stats;

    } catch (error) {
      console.error('Error fetching integration stats:', error);
      return {
        totalConversions: 0,
        tasksToGigs: 0,
        gigsToTasks: 0,
        activeAssignments: 0,
        completedAssignments: 0,
        totalEarnings: 0
      };
    }
  }

  /**
   * Check if task can be converted to gig
   */
  canConvertTaskToGig(task) {
    const checks = {
      hasTitle: !!task.title,
      hasDescription: !!task.description,
      notCompleted: task.status !== 'completed',
      notAlreadyConverted: !task.source_gig_id,
      hasProject: !!task.project_id
    };

    const canConvert = Object.values(checks).every(check => check);
    
    return {
      canConvert,
      checks,
      reasons: Object.entries(checks)
        .filter(([key, value]) => !value)
        .map(([key]) => this.getCheckFailureReason(key))
    };
  }

  /**
   * Check if gig can be converted to task
   */
  canConvertGigToTask(gig) {
    const checks = {
      hasTitle: !!gig.title,
      hasDescription: !!gig.description,
      isOpen: gig.status === 'open' || gig.status === 'in_progress',
      notAlreadyConverted: !gig.source_task_id
    };

    const canConvert = Object.values(checks).every(check => check);
    
    return {
      canConvert,
      checks,
      reasons: Object.entries(checks)
        .filter(([key, value]) => !value)
        .map(([key]) => this.getCheckFailureReason(key))
    };
  }

  /**
   * Get human-readable reason for check failure
   */
  getCheckFailureReason(checkKey) {
    const reasons = {
      hasTitle: 'Missing title',
      hasDescription: 'Missing description',
      notCompleted: 'Task is already completed',
      notAlreadyConverted: 'Already converted',
      hasProject: 'Not associated with a project',
      isOpen: 'Gig is not open or in progress'
    };

    return reasons[checkKey] || 'Unknown requirement not met';
  }
}

export const taskGigIntegrationService = new TaskGigIntegrationService();
export default taskGigIntegrationService;
